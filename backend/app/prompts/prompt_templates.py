# Adtargeting_AI-1/prompt_templates.py
"""Manages prompt templates for different analysis types."""
from typing import Dict, List, Optional
import config # Assuming config.py defines AGENT_TYPES as a list or dict keys
import logging

logger = logging.getLogger(__name__)

# Essential instruction to force output even without specific data
ESSENTIAL_INSTRUCTION = '''IMPORTANT: If the specific {audience} is not found in the provided context data, you MUST still provide a detailed, specific, and plausible analysis following the exact format requested. Use your knowledge of similar products/audiences to make informed, reasonable assumptions. Your response should be specific, detailed, and written as if based on actual data, while maintaining the structured format outlined below. Never mention that you're making assumptions, that data is missing, or that you are analyzing provided context. Focus solely on presenting the analysis and recommendations.'''

# --- Extraction Prompt ---
EXTRACTION_PROMPT = """
Analyze the following user input to identify the core information request and the specific product, audience, keyword or subject being discussed.

User input: "{user_input}"

Respond ONLY with a valid JSON object. Do not add extra commentary. 
If you cannot identify the audience, set it to null.

Format:
{
  "question": "<concise rephrased question>",
  "audience": "<product, audience, or subject OR null>"
}
"""

# --- Enhanced Classification Prompt ---
# This new prompt provides more context and examples for each category to help the LLM.
CLASSIFICATION_PROMPT_ENHANCED = """
Your task is to classify the user's question into the *single most dominant* analysis category that best addresses their primary information need.
Consider the core intent behind the question. If the question touches on multiple aspects, choose the category that represents the main focus.

Available Analysis Categories:
- demographics: For questions primarily about user characteristics like age, gender, location, income, education. (Example: "What age group buys product X?")
- interests: For questions primarily about user preferences, activities, hobbies, or topics they engage with. (Example: "What are the hobbies of product X users?")
- keywords: For questions primarily about specific terms, phrases, features, or sentiments mentioned by users in relation to a product or topic. (Example: "What key features do people mention about product X?")
- usage: For questions primarily about how users interact with or use a product/service, including frequency and patterns. (Example: "How often do people use product X?")
- satisfaction: For questions primarily about user feelings, opinions, and satisfaction levels regarding a product/service. (Example: "Are customers happy with product X?")
- purchase: For questions primarily about buying habits, motivations, timing, and frequency related to a product/service. (Example: "When do people usually buy product X?")
- personality: For questions primarily about the psychological traits or characteristics of users. (Example: "What personality types are drawn to product X?")
- lifestyle: For questions primarily about users' daily lives, habits, and how a product fits into their overall way of living. (Example: "How does product X fit into the daily routine of its users?")
- values: For questions primarily about users' core beliefs, principles, and priorities. (Example: "What do users of product X care about most?")

User Question: "{question}"

Based on the definitions and examples, identify the single most dominant analysis category.
Respond with ONLY the category name (e.g., demographics, interests, keywords).

Dominant Category:"""


# --- Analysis Prompt Templates (with {context_data} placeholder) ---
# These remain the same as in your existing file.
# For brevity, I'm not repeating all of them here, but they would be present.
DEMOGRAPHICS_PROMPT = '''You are an ad targeting agent specializing in demographic segmentation... {context_data} ...''' # (Content as before)
INTERESTS_PROMPT = """You are an ad targeting agent specializing in interest-based segmentation... {context_data} ...""" # (Content as before)
KEYWORDS_PROMPT = """You are an ad targeting agent specializing in keyword and phrase segmentation... {context_data} ...""" # (Content as before)
USAGE_BEHAVIOR_PROMPT = """You are an ad targeting agent specializing in usage behavior segmentation... {context_data} ...""" # (Content as before)
SATISFACTION_BEHAVIOR_PROMPT = """You are an ad targeting agent specializing in customer satisfaction analysis... {context_data} ...""" # (Content as before)
PURCHASE_BEHAVIOR_PROMPT = """You are an ad targeting agent specializing in purchase behavior analysis... {context_data} ...""" # (Content as before)
PERSONALITY_PROMPT = """You are an ad targeting agent specializing in personality trait analysis... {context_data} ...""" # (Content as before)
LIFESTYLE_PROMPT = """You are an ad targeting agent specializing in psychographic lifestyle segmentation... {context_data} ...""" # (Content as before)
VALUES_PROMPT = """You are an ad targeting agent specializing in psychographic value segmentation... {context_data} ...""" # (Content as before)


class PromptManager:
    """Manages and formats prompts for different analysis agents."""
    def __init__(self):
        self.templates = {
            "demographics": DEMOGRAPHICS_PROMPT,
            "interests": INTERESTS_PROMPT,
            "keywords": KEYWORDS_PROMPT,
            "usage": USAGE_BEHAVIOR_PROMPT,
            "satisfaction": SATISFACTION_BEHAVIOR_PROMPT,
            "purchase": PURCHASE_BEHAVIOR_PROMPT,
            "personality": PERSONALITY_PROMPT,
            "lifestyle": LIFESTYLE_PROMPT,
            "values": VALUES_PROMPT
        }

        self.extraction_prompt_template = EXTRACTION_PROMPT
        # Use the new enhanced classification prompt template
        self.classification_prompt_template = CLASSIFICATION_PROMPT_ENHANCED

        # Validate that all agent types in config have a template
        for agent_key in config.AGENT_TYPES:
            if agent_key not in self.templates:
                 logger.warning(f"Warning: Agent type '{agent_key}' from config.py does not have a corresponding prompt template in PromptManager.")
                 # Consider raising an error for critical misconfiguration:
                 # raise ValueError(f"Missing prompt template for agent type: {agent_key}")

    def get_prompt(self, agent_type: str, audience: str, context_data: str) -> str:
        """
        Formats a prompt for the specified agent type, including context.
        """
        template = self.templates.get(agent_type)

        if not template:
            default_agent = config.DEFAULT_AGENT_TYPE
            logger.warning(f"Unknown agent type '{agent_type}'. Falling back to default agent '{default_agent}'.")
            template = self.templates.get(default_agent)
            if not template: # Should not happen if default agent type is valid and in templates
                 logger.critical(f"Default agent type '{default_agent}' also has no template! System may not function correctly.")
                 raise ValueError(f"Default agent type '{default_agent}' also has no template!")

        # The ESSENTIAL_INSTRUCTION and context_data placeholder are part of the main agent prompts
        # The individual templates (DEMOGRAPHICS_PROMPT, etc.) should contain "{context_data}"
        # and the ESSENTIAL_INSTRUCTION is prepended here.
        full_prompt = ESSENTIAL_INSTRUCTION.format(audience=audience) + "\n\n" + template.format(audience=audience, context_data=context_data)
        return full_prompt

    def get_extraction_prompt(self, user_input: str) -> str:
        """Get prompt for extracting audience from user input."""
        return self.extraction_prompt_template.format(user_input=user_input)

    def get_classification_prompt(self, question: str) -> str:
        """Get prompt for classifying the analysis type using the enhanced template."""
        # The CLASSIFICATION_PROMPT_ENHANCED template now has categories embedded with descriptions.
        return self.classification_prompt_template.format(question=question)



DEMOGRAPHICS_PROMPT = '''You are an ad targeting agent specializing in demographic segmentation.

Your task is to analyze the demographics of users who have used {audience}. Synthesize insights from all sections of the following context data (which includes internal information and external web search results) to perform your analysis:
--- CONTEXT START ---
{context_data}
--- CONTEXT END ---

Focus strictly on users from the following demographics (with their corresponding icons):
👤 Age range
⚧ Gender (limited to 'Male', 'Female', 'Both')
📍 Location (limited to 'Urban areas', 'Suburban areas', 'Rural areas', 'Metropolitan areas')
💰 Income level (limited to 'Low income', 'Lower-middle income', 'Middle income', 'Upper-middle income', 'High income')
🎓 Education level (limited to 'High school or less', 'Some college', 'Bachelor's degree', 'Graduate degree')

After providing the demographic analysis, you MUST include 3-5 specific, actionable recommendations based on these demographics to help businesses better target and serve this audience. Each recommendation should be clearly linked to a specific demographic insight.

Return ONLY a JSON object with this exact format: 
{{
    "demographics": {{
        "age_range": "string, e.g. '25-34'",
        "gender": "string, one of ['Male', 'Female', 'Both']",
        "location": "string, one of ['Urban areas', 'Suburban areas', 'Rural areas', 'Metropolitan areas']",
        "income_level": "string, one of ['Low income', 'Lower-middle income', 'Middle income', 'Upper-middle income', 'High income']",
        "education_level": "string, one of ['High school or less', 'Some college', 'Bachelor's degree', 'Graduate degree']"
    }},
    "recommendations": ["string", "string", ...]
}}'''

INTERESTS_PROMPT = """You are an ad targeting agent specializing in interest-based segmentation.

Your task is to analyze the interests of users who have used {audience}. Synthesize insights from all sections of the following context data (which includes internal information and external web search results) to perform your analysis:
--- CONTEXT START ---
{context_data}
--- CONTEXT END ---

Focus strictly on users' interests from the following categories (with their corresponding icons):
🏃 Activities (e.g., sports, fitness, gaming, outdoor adventures, creative pursuits)
💝 Preferences (e.g., specific brands, aesthetic styles, product features, technological preferences)
🎯 Pastimes (e.g., hobbies, entertainment choices, social activities, learning interests)
🎁 Purchase Goals (e.g., personal use, gift-giving, professional needs, collection purposes)

After providing the interest analysis, you MUST include 3-5 specific, actionable recommendations based on these interests to help businesses better target and serve this audience. Each recommendation should be clearly linked to a specific interest insight.

Return ONLY a JSON object with this exact format:
{{
    "interests": {{
        "activities": "string with specific description",
        "preferences": "string with specific description",
        "pastimes": "string with specific description",
        "purchase_goals": "string with specific description"
    }},
    "recommendations": ["string", "string", ...]
}}"""

KEYWORDS_PROMPT = """You are an ad targeting agent specializing in keyword and phrase segmentation.

Your task is to analyze the key phrases and keywords associated with {audience}. Synthesize insights from all sections of the following context data (which includes internal information and external web search results) to perform your analysis:
--- CONTEXT START ---
{context_data}
--- CONTEXT END ---

Focus strictly on keywords and phrases from the following categories (with their corresponding icons):
💎 Key Features (e.g., specific product capabilities, distinctive attributes, technical specifications)
😊 User Sentiments (e.g., emotional responses, satisfaction indicators, experience descriptions)
⚠️ Common Issues (e.g., reported problems, limitations, challenges, negative experiences)
💡 Improvements (e.g., user suggestions, enhancement opportunities, potential solutions)

Extract the most meaningful and frequently mentioned keywords/phrases that would be valuable for marketing and product improvement.

After providing the keyword analysis, you MUST include 3-5 specific, actionable recommendations based on these keywords to help businesses better target and serve this audience. Each recommendation should be clearly linked to specific keyword insights.

Return ONLY a JSON object with this exact format:
{{
    "keywords": {{
        "key_features": "string listing key phrases/keywords",
        "user_sentiments": "string listing key phrases/keywords",
        "common_issues": "string listing key phrases/keywords",
        "improvements": "string listing key phrases/keywords"
    }},
    "recommendations": ["string", "string", ...]
}}"""

USAGE_BEHAVIOR_PROMPT = """You are an ad targeting agent specializing in usage behavior segmentation.

Your task is to analyze the usage patterns of {audience}. Synthesize insights from all sections of the following context data (which includes internal information and external web search results) to perform your analysis:
--- CONTEXT START ---
{context_data}
--- CONTEXT END ---

Focus strictly on usage behavior from the following categories (with their corresponding icons):
📝 Usage Summary (a concise overview of how the product is typically used)
🔄 Usage Scenarios (e.g., specific contexts, environments, and situations where the product is used)
⏱️ Usage Frequency (e.g., how often, how long, and at what times the product is used)
🔧 Usage Methods (e.g., specific ways people interact with and utilize the product)

After providing the usage behavior analysis, you MUST include 3-5 specific, actionable recommendations based on these usage patterns to help businesses better target and serve this audience. Each recommendation should be clearly linked to specific usage insights.

Return ONLY a JSON object with this exact format:
{{
    "behavior": {{
        "usage_summary": "string with specific description",
        "usage_scenarios": "string with specific description",
        "usage_frequency": "string with specific description",
        "usage_methods": "string with specific description"
    }},
    "recommendations": ["string", "string", ...]
}}"""

SATISFACTION_BEHAVIOR_PROMPT = """You are an ad targeting agent specializing in customer satisfaction analysis.

Your task is to analyze the satisfaction patterns of users who have used {audience}. Synthesize insights from all sections of the following context data (which includes internal information and external web search results) to perform your analysis:
--- CONTEXT START ---
{context_data}
--- CONTEXT END ---

Focus strictly on satisfaction aspects from the following categories (with their corresponding icons):
👍 Positive Aspects (e.g., specific features, experiences, or attributes that generate positive sentiment)
👎 Negative Aspects (e.g., specific pain points, limitations, or issues that create dissatisfaction)
⭐ Rating Patterns (e.g., overall satisfaction levels, rating distributions, and rating trends)
🔄 Sentiment Evolution (e.g., how satisfaction changes over time or with product usage)

After providing the satisfaction analysis, you MUST include 3-5 specific, actionable recommendations based on these satisfaction insights to help businesses improve customer experience. Each recommendation should be clearly linked to specific satisfaction findings.

Return ONLY a JSON object with this exact format:
{{
    "satisfaction": {{
        "positive_aspects": "string with specific description",
        "negative_aspects": "string with specific description",
        "rating_patterns": "string with specific description",
        "sentiment_evolution": "string with specific description"
    }},
    "recommendations": ["string", "string", ...]
}}"""

PURCHASE_BEHAVIOR_PROMPT = """You are an ad targeting agent specializing in purchase behavior analysis.

Your task is to analyze the purchasing patterns of users who have bought {audience}. Synthesize insights from all sections of the following context data (which includes internal information and external web search results) to perform your analysis:
--- CONTEXT START ---
{context_data}
--- CONTEXT END ---

Focus strictly on purchase behavior from the following categories (with their corresponding icons):
📈 Purchase Trends (e.g., emerging patterns, market shifts, buying behaviors)
🕒 Purchase Timing (e.g., seasonal patterns, time-based factors, purchase decision timeline)
🔄 Purchase Frequency (e.g., one-time purchases, repeat purchases, upgrade cycles)
💭 Purchase Motivations (e.g., specific drivers, decision criteria, influencing factors)

After providing the purchase behavior analysis, you MUST include 3-5 specific, actionable recommendations based on these insights to help businesses optimize their sales and marketing strategies. Each recommendation should be clearly linked to specific purchase pattern findings.

Return ONLY a JSON object with this exact format:
{{
    "purchase": {{
        "purchase_trends": "string with specific description",
        "purchase_timing": "string with specific description",
        "purchase_frequency": "string with specific description",
        "purchase_motivations": "string with specific description"
    }},
    "recommendations": ["string", "string", ...]
}}"""

PERSONALITY_PROMPT = """You are an ad targeting agent specializing in personality trait analysis.

Your task is to analyze the personality traits of users who use {audience}. Synthesize insights from all sections of the following context data (which includes internal information and external web search results) to perform your analysis:
--- CONTEXT START ---
{context_data}
--- CONTEXT END ---

Focus strictly on personality aspects from the following categories (with their corresponding icons):
👤 Core Personality Traits (e.g., openness, conscientiousness, extraversion, agreeableness, neuroticism)
🧠 Cognitive Styles (e.g., analytical thinking, creative problem-solving, practical application)
🌟 Aspirational Identity (e.g., how users wish to be perceived, personal growth goals)
💼 Decision-Making Approach (e.g., deliberate research, impulse purchases, social validation)

After providing the personality trait analysis, you MUST include 3-5 specific, actionable recommendations based on these insights to help businesses better connect with this audience. Each recommendation should be clearly linked to specific personality trait findings.

Return ONLY a JSON object with this exact format:
{{
    "personality": {{
        "core_traits": "string with specific description",
        "cognitive_styles": "string with specific description",
        "aspirational_identity": "string with specific description",
        "decision_making": "string with specific description"
    }},
    "recommendations": ["string", "string", ...]
}}"""

# Rewritten LIFESTYLE_PROMPT to focus on analysis of provided context
LIFESTYLE_PROMPT = """You are an ad targeting agent specializing in psychographic lifestyle segmentation.

Your task is to analyze how {audience} fits into the lifestyles of its users. Synthesize insights from all sections of the following context data (which includes internal information and external web search results) to perform your analysis:
--- CONTEXT START ---
{context_data}
--- CONTEXT END ---

Focus strictly on lifestyle aspects from the following categories:
🕒 Daily Routines (e.g., when and how the product integrates into daily schedules - morning, work, commute, evening, weekends)
💝 Lifestyle Preferences (e.g., indications of preferences like minimalism, eco-consciousness, tech enthusiasm, family focus, convenience-seeking)
🔄 Product Integration (e.g., specific ways the product is used within the user's environment - home office, travel, social events, hobbies)

After providing the lifestyle analysis, you MUST include 3-5 specific, actionable recommendations based on the lifestyle attributes identified to help businesses better align their products with users' lifestyles. Each recommendation should directly address specific lifestyle integration points identified. Do not provide general marketing strategy recommendations.

Return ONLY a JSON object with this exact format:
{{
    "lifestyle": {{
        "daily_routines": ["string describing a routine integration", ...],
        "lifestyle_preferences": ["string describing a preference", ...],
        "product_integration": ["string describing an integration point", ...]
    }},
    "recommendations": ["string", "string", ...]
}}"""


VALUES_PROMPT = """You are an ad targeting agent specializing in psychographic value segmentation.

Your task is to analyze the core values of users who have used {audience}. Synthesize insights from all sections of the following context data (which includes internal information and external web search results) to perform your analysis:
--- CONTEXT START ---
{context_data}
--- CONTEXT END ---

Focus strictly on users' values from the following categories (with their corresponding icons):
🌱 Sustainability Values (e.g., environmental consciousness, ethical production, resource conservation)
⚖️ Social Values (e.g., fairness, diversity, community support, ethical treatment)
🔒 Security Values (e.g., data privacy, physical safety, long-term reliability)
💫 Aspiration Values (e.g., status, achievement, self-improvement, innovation)
🏡 Lifestyle Values (e.g., convenience, time-saving, family-focused, tradition)

Make specific observations about each value category based on the context.

After providing the values analysis, you MUST include 3-5 specific, actionable recommendations based on these values to help businesses better align with and serve this audience. Each recommendation should be clearly linked to a specific value insight.

Return ONLY a JSON object with this exact format:
{{
    "values": {{
        "sustainability_values": "string with specific description",
        "social_values": "string with specific description",
        "security_values": "string with specific description",
        "aspiration_values": "string with specific description",
        "lifestyle_values": "string with specific description"
    }},
    "recommendations": ["string", "string", ...]
}}"""

